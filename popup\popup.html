<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../styles/popup.css" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="translatePopup" class="container extension-popup">
      <div>
        <div class="popup-header">
          <div class="header-title">
            <svg
              id="translateIcon"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 7c-1.5 0-2.5 1-3 2-1.5 3 1 8.5 3 8.5s4.5-5.5 3-8.5c-.5-1-1.5-2-3-2z"></path>
              <circle cx="9" cy="9" r="0.5"></circle>
              <circle cx="15" cy="9" r="0.5"></circle>
              <circle cx="5" cy="7" r="0.5"></circle>
              <circle cx="19" cy="7" r="0.5"></circle>
              <circle cx="5" cy="17" r="0.5"></circle>
              <circle cx="19" cy="17" r="0.5"></circle>
            </svg>
            <svg
              id="messageIcon"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon hidden"
            >
              <path
                d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
              ></path>
            </svg>
            <h3 id="popupTitle" class="popup-title">Web Translator</h3>
          </div>
          <div class="header-actions">
            <button id="closeBtn" class="header-btn">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
        <button id="translatePageBtn" class="translate-page-btn">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="icon"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 7c-1.5 0-2.5 1-3 2-1.5 3 1 8.5 3 8.5s4.5-5.5 3-8.5c-.5-1-1.5-2-3-2z"></path>
            <circle cx="9" cy="9" r="0.5"></circle>
            <circle cx="15" cy="9" r="0.5"></circle>
            <circle cx="5" cy="7" r="0.5"></circle>
            <circle cx="19" cy="7" r="0.5"></circle>
            <circle cx="5" cy="17" r="0.5"></circle>
            <circle cx="19" cy="17" r="0.5"></circle>
          </svg>
          <span>Translate this page</span>
        </button>
      </div>

      <!-- Tabs -->
      <div class="tabs">
        <button data-tab="translationSection" class="tab-btn active">
          Translate
        </button>
        <button data-tab="videoTranslateSection" class="tab-btn">
          Video Translate
        </button>
        <button data-tab="aiQuestionSection" class="tab-btn">Ask AI</button>
      </div>

      <!-- Content -->
      <div id="popupContent" class="popup-content">
        <div id="modelSelector" class="model-selector-container">
          <label class="model-label">AI Model</label>
          <div class="select-container">
            <select id="ai-model" class="model-select">
              <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
              <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
            </select>
            <div class="select-arrow">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
        </div>

        <!-- Translation Section -->
        <div id="translationSection" class="section">
          <div class="language-selector">
            <select id="sourceLang" class="lang-select">
              <option value="auto" selected>Auto-detect</option>
              <option value="en">English</option>
              <option value="fa">Persian</option>
              <option value="ar">Arabic</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="es">Spanish</option>
              <option value="zh">Chinese</option>
              <option value="ru">Russian</option>
            </select>
            <button id="switchLangs" class="switch-btn">
              <div class="switch-arrows">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon"
                >
                  <line x1="12" y1="19" x2="12" y2="5"></line>
                  <polyline points="5 12 12 5 19 12"></polyline>
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon"
                >
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <polyline points="19 12 12 19 5 12"></polyline>
                </svg>
              </div>
            </button>
            <select id="targetLang" class="lang-select">
              <option value="en" selected>English</option>
              <option value="fa">Persian</option>
              <option value="ar">Arabic</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="es">Spanish</option>
              <option value="zh">Chinese</option>
              <option value="ru">Russian</option>
            </select>
          </div>

          <div class="tone-selector">
            <label for="translationTone" class="tone-label">Tone</label>
            <select id="translationTone" class="tone-select">
              <option value="neutral" selected>Neutral</option>
              <option value="casual">Casual/Friendly</option>
              <option value="formal">Formal/Professional</option>
              <option value="simple">Simple/Clear</option>
              <option value="literary">Literary/Poetic</option>
            </select>
          </div>

          <div id="pageTranslationMode" class="page-translation-mode hidden">
            <p class="page-translation-text">
              Page translation is active. The entire webpage will be translated
              from <span id="sourceLanguageName">Auto-detect</span> to
              <span id="targetLanguageName">English</span>.
            </p>
            <div class="persistent-translation-option">
              <input
                type="checkbox"
                id="persistentTranslation"
                class="persistent-checkbox"
              />
              <label for="persistentTranslation"
                >Keep translating pages on this domain</label
              >
              <div class="tooltip">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
                <span class="tooltip-text"
                  >When enabled, all pages on this domain will be automatically
                  translated using the same settings.</span
                >
              </div>
            </div>
            <div id="translation-status" class="translation-status" style="display: none;">
              <span id="status-text">Initializing translation service...</span>
            </div>
            <button id="translate-page-button" class="translate-page-action">
              Translate This Page
            </button>
            <div id="troubleshooting-tip" class="troubleshooting-tip" style="display: none;">
              <p><strong>Translation not working?</strong></p>
              <ul>
                <li>Refresh the page and try again</li>
                <li>Make sure you're on a regular website (not chrome:// or about: pages)</li>
                <li>Check that your API key is set correctly</li>
              </ul>
            </div>
          </div>

          <div id="textTranslationMode" class="text-translation-mode">
            <div class="textarea-container">
              <div class="textarea-with-button">
                <textarea
                  id="sourceText"
                  class="source-textarea"
                  placeholder="Enter text to translate"
                ></textarea>
                <button id="clear-text" class="copy-btn-inside">
                  <span class="material-icons clear-text">clear</span>
                </button>
              </div>
            </div>
            <div class="textarea-container">
              <div class="textarea-with-button">
                <textarea
                  id="translatedText"
                  class="translated-textarea"
                  placeholder="Translation will appear here"
                  readonly
                ></textarea>
                <button id="copy-text" class="copy-btn-inside">
                  <span class="material-icons copy-text">content_copy</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Translate Section -->
        <div id="videoTranslateSection" class="section hidden">
          <div class="video-translate-header">
            <h4>YouTube Subtitles Translation</h4>
          </div>

          <div class="video-translate-options">
            <div class="option-group">
              <div class="toggle-option">
                <input
                  type="checkbox"
                  id="enableVideoTranslate"
                  class="toggle-checkbox"
                  checked
                />
                <label for="enableVideoTranslate" class="toggle-label"
                  >Enable YouTube Subtitles Translation</label
                >
              </div>
            </div>

            <div class="language-selector">
              <label class="language-label">Translate to:</label>
              <select id="videoTargetLang" class="lang-select">
                <option value="en">English</option>
                <option value="fa" selected>Persian</option>
                <option value="ar">Arabic</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="es">Spanish</option>
                <option value="zh">Chinese</option>
                <option value="ru">Russian</option>
              </select>
            </div>

            <div class="tone-selector">
              <label for="videoTranslateTone" class="tone-label">Tone</label>
              <select id="videoTranslateTone" class="tone-select">
                <option value="neutral" selected>Neutral</option>
                <option value="casual">Casual/Friendly</option>
                <option value="formal">Formal/Professional</option>
                <option value="simple">Simple/Clear</option>
                <option value="literary">Literary/Poetic</option>
              </select>
            </div>

            <div class="subtitle-customization-section">
              <h5 class="customization-title">Subtitle Customization</h5>

              <!-- Font Size -->
              <div class="customization-option">
                <label for="subtitle-font-size">Font Size:</label>
                <input
                  type="range"
                  id="subtitle-font-size"
                  min="12"
                  max="24"
                  value="16"
                  class="slider"
                />
                <span class="value-display" id="font-size-value">16px</span>
              </div>

              <!-- Background Color -->
              <div class="customization-option">
                <label for="subtitle-bg-opacity">Opacity:</label>
                <input
                  type="range"
                  id="subtitle-bg-opacity"
                  min="0"
                  max="100"
                  value="80"
                  class="slider"
                />
                <span class="value-display" id="bg-opacity-value">80%</span>
              </div>

              <!-- Overall Opacity -->
              <div class="customization-option">
                <label for="subtitle-opacity">Overall Opacity:</label>
                <input
                  type="range"
                  id="subtitle-opacity"
                  min="0"
                  max="100"
                  value="100"
                  class="slider"
                />
                <span class="value-display" id="opacity-value">100%</span>
              </div>
            </div>
          </div>
          <button id="saveVideoTranslateSettings" class="save-settings-btn">
            Save Settings
          </button>
          <div class="video-translate-info"></div>
        </div>

        <!-- AI Question Section -->
        <div id="aiQuestionSection" class="section hidden">
          <div class="conversation-header">
            <h4>Ask AI</h4>
            <div class="ai-controls">
              <select id="aiResponseLang" class="lang-select">
                <option value="en" selected>English</option>
                <option value="fa">Persian</option>
                <option value="ar">Arabic</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="es">Spanish</option>
                <option value="zh">Chinese</option>
                <option value="ru">Russian</option>
              </select>
              <button
                id="clearConversation"
                class="clear-conversation-btn"
                title="Clear conversation"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon"
                >
                  <polyline points="3 6 5 6 21 6"></polyline>
                  <path
                    d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                  ></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
              </button>
            </div>
          </div>
          <div class="tone-selector">
            <label for="aiTone" class="tone-label">Tone</label>
            <select id="aiTone" class="tone-select">
              <option value="neutral" selected>Neutral</option>
              <option value="casual">Casual/Friendly</option>
              <option value="formal">Formal/Professional</option>
              <option value="simple">Simple/Clear</option>
              <option value="literary">Literary/Poetic</option>
            </select>
          </div>
          <div class="conversation-container">
            <div id="conversationBox" class="conversation-box">
              <div class="empty-conversation">
                Ask any question to start a conversation
              </div>
            </div>
          </div>
          <form id="questionForm" class="question-form">
            <input
              type="text"
              id="questionInput"
              class="question-input"
              placeholder="Ask any question..."
            />
            <button type="submit" id="sendBtn" class="send-btn" disabled>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
              </svg>
            </button>
          </form>
        </div>
      </div>

      <!-- Settings Section -->
      <div id="settingsSection" class="settings-section">
        <!-- API Key Section -->
        <div id="apiKeySection" class="api-key-section">
          <div class="api-key-container">
            <label for="apiKeyInput" class="api-key-label"
              >Gemini API Key</label
            >
            <div class="api-key-input-container">
              <input
                type="password"
                id="apiKeyInput"
                class="api-key-input"
                placeholder="Enter your Gemini API key"
              />
              <button id="toggleApiKeyVisibility" class="toggle-visibility-btn">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon"
                >
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
            </div>
            <div class="api-key-actions">
              <button id="saveApiKey" class="save-api-key-btn">
                Save API Key
              </button>
              <a
                href="https://ai.google.dev/"
                target="_blank"
                class="get-api-key-link"
                >Get API Key</a
              >
            </div>
            <div id="api-key-status" class="api-key-status"></div>
          </div>
        </div>

        <!-- YouTube Subtitles Section has been moved to Video Translate tab -->

        <!-- Keyboard Shortcuts Section -->
        <div id="keyboardShortcutsSection" class="keyboard-shortcuts-section">
          <div class="shortcuts-container">
            <div class="shortcuts-header">
              <h4 class="shortcuts-title">Keyboard Shortcuts</h4>
              <div class="shortcuts-toggle">
                <input
                  type="checkbox"
                  id="enableShortcuts"
                  class="toggle-checkbox"
                  checked
                />
                <label for="enableShortcuts" class="toggle-label">Enable</label>
              </div>
            </div>

            <div class="shortcut-item">
              <div class="shortcut-description">Translate current page:</div>
              <div class="shortcut-key-container">
                <div class="shortcut-key-combo">
                  <label class="modifier-checkbox">
                    <input type="checkbox" id="shortcutShift" checked />
                    <span>Shift</span>
                  </label>
                  <label class="modifier-checkbox">
                    <input type="checkbox" id="shortcutCtrl" />
                    <span>Ctrl</span>
                  </label>
                  <label class="modifier-checkbox">
                    <input type="checkbox" id="shortcutAlt" />
                    <span>Alt</span>
                  </label>
                  <span class="plus-sign">+</span>
                  <input
                    type="text"
                    id="shortcutKey"
                    class="key-input"
                    value="T"
                    maxlength="1"
                  />
                </div>
                <button id="saveShortcut" class="save-shortcut-btn">
                  Save
                </button>
              </div>
              <div id="shortcut-status" class="shortcut-status"></div>
              <div class="shortcut-note">
                <span class="note-icon">ℹ️</span>
                <span class="note-text"
                  >This shortcut will not work on mobile devices.</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="../scripts/popup.js"></script>
    <script src="../scripts/popup-video-translate.js"></script>
  </body>
</html>
