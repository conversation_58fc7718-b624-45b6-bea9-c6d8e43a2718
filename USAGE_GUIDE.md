# راهنمای استفاده از ترجمه زیرنویس YouTube

## 🚀 نصب و راه‌اندازی

### 1. لود کردن Extension
1. Firefox را باز کنید
2. `about:debugging` را در نوار آدرس تایپ کنید
3. روی "This Firefox" کلیک کنید
4. روی "Load Temporary Add-on" کلیک کنید
5. فایل `manifest.json` را انتخاب کنید

### 2. تنظیم API Key
1. روی آیکون extension در نوار ابزار کلیک کنید
2. کلید API Gemini خود را وارد کنید
3. ز<PERSON><PERSON> مقصد را انتخاب کنید (پیش‌فرض: فارسی)

## 🎯 نحوه استفاده

### در YouTube:
1. به هر ویدیو YouTube بروید
2. زیرنویس را فعال کنید (دکمه CC)
3. ترجمه‌ها خودکار در پایین صفحه نمایش داده می‌شوند

## ✨ ویژگی‌های جدید

### 🔧 بهبودهای انجام شده:
- **سرعت بالاتر**: تاخیر کاهش یافته از 1000ms به 500ms
- **ترجمه کامل‌تر**: prompt بهبود یافته برای ترجمه کامل
- **تنظیمات API بهتر**: maxOutputTokens افزایش یافته به 4096
- **نظارت هوشمند**: MutationObserver + Polling
- **نمایش بهتر**: زمان نمایش بر اساس طول متن

### 📱 ظاهر بهبود یافته:
- **موقعیت**: پایین وسط صفحه (120px از پایین)
- **استایل**: پس‌زمینه تیره با حاشیه آبی
- **فونت**: Segoe UI, 20px, وزن 500
- **جهت**: RTL برای فارسی
- **افکت**: backdrop-filter blur و سایه

## 🔍 عیب‌یابی

### اگر ترجمه نمایش داده نمی‌شود:
1. کنسول مرورگر را باز کنید (F12)
2. به دنبال این پیام‌ها بگردید:
   - `🎬 YouTube Subtitles Script Starting...`
   - `✅ On YouTube, initializing...`
   - `⚙️ Settings loaded - API Key: Available`

### اگر ترجمه ناقص است:
- API key معتبر باشد
- اتصال اینترنت پایدار باشد
- در کنسول خطای API نباشد

### اگر تاخیر زیاد است:
- Extension را reload کنید
- صفحه YouTube را refresh کنید
- کش مرورگر را پاک کنید

## 📊 لاگ‌های مفید

### لاگ‌های عادی:
```
🎬 YouTube Subtitles Script Starting...
✅ On YouTube, initializing...
⚙️ Settings loaded - API Key: Available
✅ Translation overlay created
🔍 Starting subtitle monitoring...
👁️ Observer detected new subtitle: Hello world
🌐 Translating: Hello world
✅ Translation: سلام دنیا
```

### لاگ‌های خطا:
```
❌ Translation failed: 401 (API key invalid)
❌ Translation failed - Network Error
⚠️ کلید API تنظیم نشده
```

## ⚡ نکات بهینه‌سازی

1. **کش ترجمه**: ترجمه‌های تکراری ذخیره می‌شوند
2. **تاخیر کنترل شده**: حداقل 500ms بین ترجمه‌ها
3. **نظارت دوگانه**: Observer + Polling برای اطمینان
4. **پاکسازی خودکار**: حذف پیشوندهای اضافی از ترجمه

## 🎨 سفارشی‌سازی

برای تغییر ظاهر، فایل `scripts/youtube-subtitles.js` را ویرایش کنید:

```javascript
// تغییر موقعیت
bottom: 120px;

// تغییر اندازه فونت
font-size: 20px;

// تغییر رنگ پس‌زمینه
background: rgba(0, 0, 0, 0.9);
```

## 📞 پشتیبانی

در صورت مشکل:
1. کنسول مرورگر را بررسی کنید
2. Extension را reload کنید
3. API key را دوباره تنظیم کنید
