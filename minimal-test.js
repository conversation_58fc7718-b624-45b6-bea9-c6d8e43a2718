// Minimal test to check if the script loads
console.log('=== MINIMAL TEST START ===');

// Mock required globals
global.window = {
    location: {
        hostname: 'www.youtube.com',
        pathname: '/watch',
        href: 'https://www.youtube.com/watch?v=test'
    },
    addEventListener: function() {},
    chrome: {
        storage: {
            local: {
                get: function(keys, callback) {
                    callback({
                        geminiApiKey: "test-key",
                        lastTargetLang: "fa",
                        youtubeSubtitlesEnabled: true,
                        videoTranslateTone: "neutral"
                    });
                },
                set: function() {}
            }
        }
    }
};

global.document = {
    readyState: 'complete',
    addEventListener: function() {},
    querySelector: function() { return null; },
    createElement: function(tag) {
        return {
            style: {},
            appendChild: function() {},
            remove: function() {},
            addEventListener: function() {},
            querySelector: function() { return null; },
            innerHTML: '',
            textContent: '',
            title: '',
            dir: ''
        };
    },
    body: {
        appendChild: function() {}
    }
};

global.chrome = global.window.chrome;
global.browser = undefined;

console.log('Globals set up');

try {
    // Load the script
    require('./scripts/youtube-subtitles.js');
    console.log('Script loaded successfully');
} catch (error) {
    console.error('Error loading script:', error);
}

console.log('=== MINIMAL TEST END ===');
