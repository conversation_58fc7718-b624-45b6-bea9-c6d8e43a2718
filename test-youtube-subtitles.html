<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Subtitles Test</title>
</head>
<body>
    <h1>YouTube Subtitles Test</h1>
    <p>Open browser console to see the logs</p>
    
    <!-- Mock YouTube elements -->
    <div id="mock-youtube">
        <h1 class="title">
            <yt-formatted-string>Test Video Title</yt-formatted-string>
        </h1>
        <video controls>
            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
        </video>
        <div class="ytp-caption-segment">Test subtitle text</div>
    </div>

    <!-- Mock browser API -->
    <script>
        // Mock browser API for testing
        window.chrome = {
            storage: {
                local: {
                    get: (keys, callback) => {
                        console.log('🔧 Mock storage.get called with keys:', keys);
                        // Mock settings
                        const mockData = {
                            geminiApiKey: "test-api-key-12345",
                            lastTargetLang: "fa",
                            youtubeSubtitlesEnabled: true,
                            videoTranslateTone: "neutral",
                            subtitleFontSize: "16px",
                            subtitleOpacity: "0.8"
                        };
                        console.log('🔧 Mock returning data:', mockData);
                        setTimeout(() => callback(mockData), 100);
                    },
                    set: (data, callback) => {
                        console.log('💾 Saving settings:', data);
                        if (callback) callback();
                    }
                }
            }
        };

        // Mock YouTube hostname
        Object.defineProperty(window.location, 'hostname', {
            writable: true,
            value: 'www.youtube.com'
        });

        Object.defineProperty(window.location, 'pathname', {
            writable: true,
            value: '/watch'
        });

        Object.defineProperty(window.location, 'href', {
            writable: true,
            value: 'https://www.youtube.com/watch?v=test'
        });
    </script>

    <!-- Load the YouTube subtitles script -->
    <script src="scripts/youtube-subtitles.js"></script>

    <script>
        // Additional test logging
        console.log('Test page loaded');
        console.log('Location hostname:', window.location.hostname);
        console.log('Location pathname:', window.location.pathname);
        
        // Test initialization after a delay
        setTimeout(() => {
            console.log('Testing manual initialization...');
            if (window.initializeYouTubeTranslator) {
                window.initializeYouTubeTranslator();
            }
        }, 2000);
    </script>
</body>
</html>
