<!DOCTYPE html>
<html>
<head>
    <title>Simple YouTube Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .video-container { margin: 20px 0; }
        .subtitle-test { 
            background: #f0f0f0; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
        }
    </style>
</head>
<body>
    <h1>Simple YouTube Subtitles Test</h1>
    
    <div class="video-container">
        <h2>Simulated YouTube Player</h2>
        <div style="background: black; width: 500px; height: 300px; position: relative; margin: 20px 0;">
            <div style="color: white; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                Video Player Simulation
            </div>
            
            <!-- Simulated YouTube subtitle -->
            <div class="ytp-caption-segment" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 3px;">
                Hello, this is a test subtitle
            </div>
        </div>
    </div>
    
    <div class="subtitle-test">
        <h3>Test Controls</h3>
        <button onclick="changeSubtitle()">Change Subtitle</button>
        <button onclick="clearSubtitle()">Clear Subtitle</button>
        <button onclick="testApiKey()">Test API Key</button>
    </div>
    
    <div id="logs" style="background: #f9f9f9; padding: 10px; margin: 20px 0; border-radius: 5px; max-height: 300px; overflow-y: auto;"></div>
    
    <script>
        // Mock YouTube environment
        Object.defineProperty(window.location, 'hostname', {
            value: 'www.youtube.com',
            writable: false
        });
        
        // Mock chrome API
        window.chrome = {
            storage: {
                local: {
                    get: function(keys, callback) {
                        console.log('📦 Chrome storage get called for:', keys);
                        setTimeout(() => {
                            callback({
                                geminiApiKey: "test-api-key-12345",
                                lastTargetLang: "fa"
                            });
                        }, 100);
                    },
                    set: function(data, callback) {
                        console.log('💾 Chrome storage set called with:', data);
                        if (callback) callback();
                    }
                }
            }
        };
        
        // Enhanced logging
        const logContainer = document.getElementById('logs');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(message, type = 'log') {
            const div = document.createElement('div');
            div.style.cssText = `
                margin: 2px 0;
                padding: 5px;
                border-radius: 3px;
                font-family: monospace;
                font-size: 12px;
                ${type === 'error' ? 'background: #ffebee; color: #c62828;' : 'background: #e8f5e8; color: #2e7d32;'}
            `;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        // Test functions
        const testSubtitles = [
            "Hello, this is a test subtitle",
            "Welcome to our YouTube channel",
            "Please subscribe and like this video",
            "Thank you for watching",
            "See you in the next video"
        ];
        
        let currentIndex = 0;
        
        function changeSubtitle() {
            const subtitleElement = document.querySelector('.ytp-caption-segment');
            if (subtitleElement) {
                currentIndex = (currentIndex + 1) % testSubtitles.length;
                subtitleElement.textContent = testSubtitles[currentIndex];
                console.log('🔄 Changed subtitle to:', testSubtitles[currentIndex]);
            }
        }
        
        function clearSubtitle() {
            const subtitleElement = document.querySelector('.ytp-caption-segment');
            if (subtitleElement) {
                subtitleElement.textContent = '';
                console.log('🗑️ Cleared subtitle');
            }
        }
        
        function testApiKey() {
            if (window.chrome && window.chrome.storage) {
                window.chrome.storage.local.get(['geminiApiKey'], (result) => {
                    console.log('🔑 API Key test:', result.geminiApiKey ? 'Found' : 'Not found');
                });
            }
        }
        
        console.log('🎬 Test page loaded');
    </script>
    
    <!-- Load the extension script -->
    <script src="scripts/youtube-subtitles.js"></script>
    
    <script>
        // Auto-change subtitles for testing
        setTimeout(() => {
            console.log('🚀 Starting auto subtitle changes...');
            setInterval(() => {
                changeSubtitle();
            }, 8000);
        }, 3000);
    </script>
</body>
</html>
