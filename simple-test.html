<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple YouTube Subtitles Test</title>
</head>
<body>
    <h1>Simple Test</h1>
    <p>Check console for logs</p>

    <script>
        // Mock chrome API
        window.chrome = {
            storage: {
                local: {
                    get: (keys, callback) => {
                        console.log('Mock get called');
                        callback({
                            geminiApiKey: "test-key",
                            lastTargetLang: "fa",
                            youtubeSubtitlesEnabled: true,
                            videoTranslateTone: "neutral"
                        });
                    },
                    set: (data, callback) => {
                        console.log('Mock set called');
                        if (callback) callback();
                    }
                }
            }
        };

        // Mock location
        Object.defineProperty(window.location, 'hostname', {
            value: 'www.youtube.com',
            writable: true,
            configurable: true
        });
        Object.defineProperty(window.location, 'pathname', {
            value: '/watch',
            writable: true,
            configurable: true
        });
        Object.defineProperty(window.location, 'href', {
            value: 'https://www.youtube.com/watch?v=test',
            writable: true,
            configurable: true
        });

        console.log('Test setup complete');
    </script>

    <script src="scripts/youtube-subtitles.js"></script>

    <script>
        setTimeout(() => {
            console.log('Manual test after 3 seconds');
            if (window.initializeYouTubeTranslator) {
                console.log('Calling initializeYouTubeTranslator manually');
                window.initializeYouTubeTranslator();
            } else {
                console.log('initializeYouTubeTranslator not found');
            }
        }, 3000);
    </script>
</body>
</html>
