// YouTube Subtitles Translator - Simple Version
console.log('🎬 YouTube Subtitles Script Starting...');

// Check if we're on YouTube
if (!window.location.hostname.includes('youtube.com')) {
    console.log('❌ Not on YouTube, exiting');
} else {
    console.log('✅ On YouTube, initializing...');
    initializeSimpleTranslator();
}

function initializeSimpleTranslator() {
    const browserAPI = typeof browser !== "undefined" ? browser : (typeof chrome !== "undefined" ? chrome : null);
    
    // Simple state management
    let currentSubtitle = '';
    let apiKey = '';
    let targetLanguage = 'fa';
    let translationCache = new Map();
    let isTranslating = false;
    let translationDiv = null;
    
    // Load settings
    loadSimpleSettings();
    
    // Create translation overlay
    createTranslationOverlay();
    
    // Start monitoring
    startSubtitleMonitoring();
    
    function loadSimpleSettings() {
        if (browserAPI && browserAPI.storage) {
            browserAPI.storage.local.get(['geminiApiKey', 'lastTargetLang'], (result) => {
                apiKey = result.geminiApiKey || '';
                targetLanguage = result.lastTargetLang || 'fa';
                console.log('⚙️ Settings loaded - API Key:', apiKey ? 'Available' : 'Missing');
            });
        } else {
            console.log('⚠️ Browser API not available');
        }
    }
    
    function createTranslationOverlay() {
        // Remove existing overlay
        const existing = document.getElementById('youtube-translation-overlay');
        if (existing) existing.remove();
        
        // Create new overlay
        translationDiv = document.createElement('div');
        translationDiv.id = 'youtube-translation-overlay';
        translationDiv.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 18px;
            font-family: Arial, sans-serif;
            z-index: 9999;
            display: none;
            max-width: 80%;
            text-align: center;
            direction: rtl;
            line-height: 1.4;
            border: 2px solid #1a73e8;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(translationDiv);
        console.log('✅ Translation overlay created');
    }
    
    function startSubtitleMonitoring() {
        console.log('🔍 Starting subtitle monitoring...');
        
        let monitorCount = 0;
        setInterval(() => {
            monitorCount++;
            
            // Find subtitle element with multiple selectors
            const selectors = [
                '.ytp-caption-segment',
                '.caption-window',
                '.ytp-caption-window-container .ytp-caption-segment',
                '.html5-video-player .ytp-caption-segment',
                '[class*="caption-segment"]'
            ];
            
            let subtitleElement = null;
            for (const selector of selectors) {
                subtitleElement = document.querySelector(selector);
                if (subtitleElement) break;
            }
            
            // Log every 20 iterations (10 seconds)
            if (monitorCount % 20 === 0) {
                console.log('🔄 Monitoring... Subtitle found:', !!subtitleElement);
            }
            
            if (subtitleElement) {
                const subtitleText = subtitleElement.textContent.trim();
                
                if (subtitleText && subtitleText !== currentSubtitle) {
                    console.log('📝 New subtitle:', subtitleText);
                    currentSubtitle = subtitleText;
                    handleNewSubtitle(subtitleText);
                }
            }
        }, 500);
    }
    
    async function handleNewSubtitle(text) {
        // Show original text immediately
        showTranslation(`🔄 ${text}`, true);
        
        // Check cache first
        if (translationCache.has(text)) {
            const translation = translationCache.get(text);
            showTranslation(translation);
            return;
        }
        
        // Translate if API key is available
        if (apiKey && !isTranslating) {
            await translateText(text);
        } else if (!apiKey) {
            showTranslation('⚠️ API Key not set - Go to extension settings');
        }
    }
    
    async function translateText(text) {
        if (isTranslating) return;
        
        isTranslating = true;
        console.log('🌐 Translating:', text);
        
        try {
            const prompt = `Translate this text to Persian (Farsi). Return ONLY the translated text: "${text}"`;
            
            const response = await fetch(
                `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 1024,
                        },
                    }),
                }
            );
            
            if (response.ok) {
                const data = await response.json();
                if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
                    const translation = data.candidates[0].content.parts[0].text.trim();
                    
                    // Cache the translation
                    translationCache.set(text, translation);
                    
                    // Show translation
                    showTranslation(translation);
                    
                    console.log('✅ Translation:', translation);
                } else {
                    showTranslation('❌ Translation failed - Invalid response');
                }
            } else {
                console.error('❌ Translation failed:', response.status);
                showTranslation('❌ Translation failed - API Error');
            }
        } catch (error) {
            console.error('❌ Translation error:', error);
            showTranslation('❌ Translation failed - Network Error');
        } finally {
            isTranslating = false;
        }
    }
    
    function showTranslation(translation, isOriginal = false) {
        if (translationDiv) {
            translationDiv.textContent = translation;
            translationDiv.style.display = 'block';
            
            // Different styling for original vs translated
            if (isOriginal) {
                translationDiv.style.background = 'rgba(255, 152, 0, 0.8)';
                translationDiv.style.borderColor = '#ff9800';
            } else {
                translationDiv.style.background = 'rgba(0, 0, 0, 0.8)';
                translationDiv.style.borderColor = '#1a73e8';
            }
            
            // Hide after 6 seconds
            setTimeout(() => {
                if (translationDiv) {
                    translationDiv.style.display = 'none';
                }
            }, 6000);
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('📄 DOM loaded, reinitializing...');
        if (window.location.hostname.includes('youtube.com')) {
            initializeSimpleTranslator();
        }
    });
}

// Handle YouTube navigation
window.addEventListener('yt-navigate-finish', () => {
    console.log('🔄 YouTube navigation detected, reinitializing...');
    setTimeout(() => {
        if (window.location.hostname.includes('youtube.com')) {
            initializeSimpleTranslator();
        }
    }, 1000);
});

console.log('🎉 YouTube Subtitles Script Loaded Successfully');
