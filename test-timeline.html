<!DOCTYPE html>
<html>
<head>
    <title>Timeline YouTube Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .video-container { margin: 20px 0; }
        .controls { margin: 20px 0; }
        button { margin: 5px; padding: 10px; }
        #logs { 
            background: #f9f9f9; 
            padding: 10px; 
            margin: 20px 0; 
            border-radius: 5px; 
            max-height: 400px; 
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Timeline-Based YouTube Subtitles Test</h1>
    
    <div class="video-container">
        <h2>Simulated YouTube Video with Timeline</h2>
        <video id="testVideo" controls style="width: 600px; height: 400px;">
            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
            
            <!-- Simulated subtitle track -->
            <track kind="subtitles" src="data:text/vtt;charset=utf-8,WEBVTT%0A%0A00:00:00.000 --> 00:00:03.000%0AH<PERSON>, welcome to our channel%0A%0A00:00:03.000 --> 00:00:06.000%0AToday we will learn something new%0A%0A00:00:06.000 --> 00:00:09.000%0APlease subscribe and like this video%0A%0A00:00:09.000 --> 00:00:12.000%0AThank you for watching%0A%0A00:00:12.000 --> 00:00:15.000%0ASee you in the next video" srclang="en" label="English" default>
        </video>
        
        <div class="controls">
            <button onclick="playVideo()">Play Video</button>
            <button onclick="pauseVideo()">Pause Video</button>
            <button onclick="seekTo(0)">Seek to Start</button>
            <button onclick="seekTo(5)">Seek to 5s</button>
            <button onclick="seekTo(10)">Seek to 10s</button>
            <button onclick="testSubtitleExtraction()">Test Subtitle Extraction</button>
        </div>
    </div>
    
    <div id="logs"></div>
    
    <script>
        // Mock YouTube environment
        Object.defineProperty(window.location, 'hostname', {
            value: 'www.youtube.com',
            writable: false
        });
        
        // Mock chrome API
        window.chrome = {
            storage: {
                local: {
                    get: function(keys, callback) {
                        console.log('📦 Chrome storage get called for:', keys);
                        setTimeout(() => {
                            callback({
                                geminiApiKey: "test-api-key-12345",
                                lastTargetLang: "fa"
                            });
                        }, 100);
                    },
                    set: function(data, callback) {
                        console.log('💾 Chrome storage set called with:', data);
                        if (callback) callback();
                    }
                }
            }
        };
        
        // Enhanced logging
        const logContainer = document.getElementById('logs');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(message, type = 'log') {
            const div = document.createElement('div');
            div.style.cssText = `
                margin: 2px 0;
                padding: 3px 5px;
                border-radius: 3px;
                ${type === 'error' ? 'background: #ffebee; color: #c62828;' : 'background: #e8f5e8; color: #2e7d32;'}
            `;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        // Video control functions
        const video = document.getElementById('testVideo');
        
        function playVideo() {
            video.play();
            console.log('▶️ Video playing');
        }
        
        function pauseVideo() {
            video.pause();
            console.log('⏸️ Video paused');
        }
        
        function seekTo(time) {
            video.currentTime = time;
            console.log(`⏭️ Seeked to ${time}s`);
        }
        
        function testSubtitleExtraction() {
            const tracks = video.textTracks;
            console.log(`🔍 Found ${tracks.length} text tracks`);
            
            for (let i = 0; i < tracks.length; i++) {
                const track = tracks[i];
                console.log(`Track ${i}: kind=${track.kind}, language=${track.language}, label=${track.label}`);
                
                if (track.cues) {
                    console.log(`  Cues: ${track.cues.length}`);
                    for (let j = 0; j < track.cues.length; j++) {
                        const cue = track.cues[j];
                        console.log(`    [${j}] ${cue.startTime}s-${cue.endTime}s: "${cue.text}"`);
                    }
                }
            }
        }
        
        // Monitor video time
        video.addEventListener('timeupdate', () => {
            const currentTime = video.currentTime;
            // Log every 2 seconds to avoid spam
            if (Math.floor(currentTime) % 2 === 0 && currentTime % 1 < 0.1) {
                console.log(`⏰ Video time: ${currentTime.toFixed(1)}s`);
            }
        });
        
        console.log('🎬 Timeline test page loaded');
    </script>
    
    <!-- Load the extension script -->
    <script src="scripts/youtube-subtitles.js"></script>
    
    <script>
        setTimeout(() => {
            console.log('🚀 Starting automatic tests...');
            testSubtitleExtraction();
        }, 2000);
    </script>
</body>
</html>
