<!DOCTYPE html>
<html>
<head>
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="log"></div>
    
    <script>
        // Custom logger
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToPage(message, type = 'log') {
            const logDiv = document.getElementById('log');
            const p = document.createElement('p');
            p.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logDiv.appendChild(p);
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToPage(args.join(' '), 'warn');
        };
        
        console.log('Debug test started');
        
        // Mock chrome
        window.chrome = {
            storage: {
                local: {
                    get: function(keys, callback) {
                        console.log('Chrome storage get called');
                        callback({
                            geminiApiKey: "test-key",
                            lastTargetLang: "fa",
                            youtubeSubtitlesEnabled: true,
                            videoTranslateTone: "neutral"
                        });
                    },
                    set: function(data, callback) {
                        console.log('Chrome storage set called');
                        if (callback) callback();
                    }
                }
            }
        };
        
        // Mock location to be YouTube
        delete window.location;
        window.location = {
            hostname: 'www.youtube.com',
            pathname: '/watch',
            href: 'https://www.youtube.com/watch?v=test'
        };
        
        console.log('Mocks created');
    </script>
    
    <script src="scripts/youtube-subtitles.js"></script>
    
    <script>
        console.log('Script loaded, checking global scope...');
        console.log('window.initializeYouTubeTranslator exists:', typeof window.initializeYouTubeTranslator);
        
        setTimeout(() => {
            console.log('Manual initialization attempt...');
            if (window.initializeYouTubeTranslator) {
                window.initializeYouTubeTranslator();
            }
        }, 2000);
    </script>
</body>
</html>
