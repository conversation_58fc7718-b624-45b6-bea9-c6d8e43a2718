// YouTube Subtitles Translator - Enhanced with Video Detection
const browserAPI = typeof browser !== "undefined" ? browser : (typeof chrome !== "undefined" ? chrome : null);
console.log('🎬 YouTube Subtitles Script Loaded');
console.log('🔧 Browser API:', browserAPI ? 'Available' : 'Not Available');
// ===================
// CORE STATE MANAGEMENT
// ===================

const createInitialState = () => ({
  apiKey: "",
  targetLanguage: "fa", 
  subtitleCache: new Map(),
  lastSubtitleText: "",
  isEnabled: true,
  isTranslating: false,
  currentSubtitleElement: null,
  translationTone: "neutral",
  currentSubtitleId: 0,
  showOriginal: false,
  videoDetected: false,
  isVideoPlaying: false,
  videoTitle: "",
  videoUrl: "",
  allSubtitles: [], // Store all extracted subtitles
  subtitleTrack: null, // Current subtitle track
});

// ===================
// VIDEO DETECTION FUNCTIONS
// ===================

const detectVideoPlaying = () => {
  const video = document.querySelector('video');
  if (!video) return false;
  
  return !video.paused && !video.ended && video.readyState > 2;
};

const getVideoInfo = () => {
  // Try multiple selectors for video title
  const titleSelectors = [
    'h1.title yt-formatted-string',
    'h1.ytd-video-primary-info-renderer',
    'h1.style-scope.ytd-video-primary-info-renderer',
    'h1[class*="title"]',
    '.title.style-scope.ytd-video-primary-info-renderer'
  ];

  let title = 'Unknown Video';
  for (const selector of titleSelectors) {
    const titleElement = document.querySelector(selector);
    if (titleElement && titleElement.textContent.trim()) {
      title = titleElement.textContent.trim();
      break;
    }
  }

  const url = window.location.href;
  return { title, url };
};

const logVideoDetection = (videoInfo) => {
  console.log('🎬 YouTube Video Detected:');
  console.log(`📺 Title: ${videoInfo.title}`);
  console.log(`🔗 URL: ${videoInfo.url}`);
  console.log(`▶️ Video is playing: ${detectVideoPlaying()}`);
  console.log('📝 Starting subtitle extraction...');
};

// ===================
// SUBTITLE EXTRACTION FUNCTIONS
// ===================

const extractSubtitleTrack = async () => {
  try {
    // Try to get subtitle track from video element
    const video = document.querySelector('video');
    if (!video) return null;

    const tracks = video.textTracks;
    for (let i = 0; i < tracks.length; i++) {
      const track = tracks[i];
      if (track.kind === 'subtitles' || track.kind === 'captions') {
        track.mode = 'showing'; // Enable the track
        return track;
      }
    }
    return null;
  } catch (error) {
    console.error('Error extracting subtitle track:', error);
    return null;
  }
};

const extractAllSubtitles = async (track) => {
  if (!track || !track.cues) return [];
  
  const subtitles = [];
  const cues = track.cues;
  
  for (let i = 0; i < cues.length; i++) {
    const cue = cues[i];
    subtitles.push({
      startTime: cue.startTime,
      endTime: cue.endTime,
      text: cue.text,
      index: i
    });
  }
  
  console.log(`📝 Extracted ${subtitles.length} subtitle segments`);
  return subtitles;
};

const getSubtitleFromDOM = () => {
  const subtitleElement = document.querySelector('.ytp-caption-segment');
  return subtitleElement ? subtitleElement.textContent.trim() : '';
};

// ===================
// ENHANCED TRANSLATION WITH TIMING
// ===================

const translateSubtitleWithTiming = async (subtitle, state) => {
  if (!state.apiKey || state.isTranslating) return null;

  // Check cache first
  if (state.subtitleCache.has(subtitle.text)) {
    return {
      ...subtitle,
      translatedText: state.subtitleCache.get(subtitle.text)
    };
  }

  try {
    let prompt = `Translate this YouTube subtitle to ${getLanguageName(state.targetLanguage)}`;
    const toneInstruction = getToneInstructions(state.translationTone);
    if (toneInstruction) {
      prompt += `. ${toneInstruction}`;
    }
    prompt += `. Return ONLY the translated text: "${subtitle.text}"`;

    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${state.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Priority: "high",
        },
        body: JSON.stringify({
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 1024,
          },
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
      const translatedText = cleanTranslatedText(
        data.candidates[0].content.parts[0].text
      );

      // Cache translation
      state.subtitleCache.set(subtitle.text, translatedText);

      return {
        ...subtitle,
        translatedText: translatedText
      };
    }
  } catch (error) {
    console.error('Translation error:', error);
  }
  
  return null;
};

// ===================
// CONSOLE DISPLAY FUNCTIONS
// ===================

const displaySubtitleInConsole = (subtitle, isTranslated = false) => {
  const timeStr = `${formatTime(subtitle.startTime)} --> ${formatTime(subtitle.endTime)}`;
  const prefix = isTranslated ? '🔄 [TRANSLATED]' : '📝 [ORIGINAL]';
  const text = isTranslated ? subtitle.translatedText : subtitle.text;
  
  console.log(`${prefix} ${timeStr}`);
  console.log(`${text}`);
  console.log('---');
};

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
};

// ===================
// MAIN PROCESSING FUNCTIONS (ENHANCED)
// ===================

const processVideoDetection = async (state, ui, timers, observers) => {
  if (!isVideoPage()) return;

  const videoInfo = getVideoInfo();
  const isPlaying = detectVideoPlaying();
  
  if (!state.videoDetected || videoInfo.url !== state.videoUrl) {
    state.videoDetected = true;
    state.videoUrl = videoInfo.url;
    state.videoTitle = videoInfo.title;
    
    logVideoDetection(videoInfo);
    
    // Wait a bit for subtitles to load
    setTimeout(async () => {
      await extractAndTranslateSubtitles(state);
    }, 2000);
  }

  state.isVideoPlaying = isPlaying;
};

const extractAndTranslateSubtitles = async (state) => {
  console.log('🔍 Attempting to extract subtitles...');
  
  // Try to get subtitle track
  const track = await extractSubtitleTrack();
  
  if (track) {
    console.log('✅ Subtitle track found');
    const subtitles = await extractAllSubtitles(track);
    
    if (subtitles.length > 0) {
      state.allSubtitles = subtitles;
      state.subtitleTrack = track;
      
      console.log('🚀 Starting translation process...');
      await translateAllSubtitles(subtitles, state);
    } else {
      console.log('⚠️ No subtitles found in track');
    }
  } else {
    console.log('⚠️ No subtitle track available');
    console.log('🔄 Falling back to DOM subtitle monitoring...');
  }
};

const translateAllSubtitles = async (subtitles, state) => {
  console.log(`🔄 Translating ${subtitles.length} subtitles...`);
  
  for (let i = 0; i < subtitles.length; i++) {
    const subtitle = subtitles[i];
    
    // Display original subtitle
    displaySubtitleInConsole(subtitle, false);
    
    // Translate and display
    const translatedSubtitle = await translateSubtitleWithTiming(subtitle, state);
    
    if (translatedSubtitle) {
      displaySubtitleInConsole(translatedSubtitle, true);
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('✅ Translation complete!');
};

// ===================
// ENHANCED MONITORING
// ===================

const startEnhancedSubtitleMonitoring = (state, ui, timers, observers) => {
  if (timers.monitoringInterval) {
    clearInterval(timers.monitoringInterval);
  }

  timers.monitoringInterval = setInterval(() => {
    if (!state.isEnabled) return;

    // Check for video detection
    processVideoDetection(state, ui, timers, observers);

    // Continue with existing DOM-based subtitle monitoring
    const ytSubtitleElement = document.querySelector(".ytp-caption-segment");
    if (!ytSubtitleElement) return;

    if (ytSubtitleElement !== state.currentSubtitleElement) {
      state.currentSubtitleElement = ytSubtitleElement;
      observeSubtitleElement(ytSubtitleElement, state, ui, timers, observers);
    }

    const subtitleText = ytSubtitleElement.textContent.trim();
    if (!subtitleText || subtitleText === state.lastSubtitleText) return;

    // Enhanced processing with timing info
    processNewSubtitleWithTiming(subtitleText, state, ui, timers);
  }, 500); // Reduced frequency for better performance
};

const processNewSubtitleWithTiming = async (text, state, ui, timers) => {
  state.lastSubtitleText = text;
  state.currentSubtitleId++;
  const subtitleId = state.currentSubtitleId;

  // Get current video time for timing
  const video = document.querySelector('video');
  const currentTime = video ? video.currentTime : 0;

  // Log current subtitle with timing
  console.log(`⏱️ [${formatTime(currentTime)}] Current subtitle: ${text}`);

  // Check cache
  if (state.subtitleCache.has(text)) {
    const translatedText = state.subtitleCache.get(text);
    console.log(`🔄 [${formatTime(currentTime)}] Cached translation: ${translatedText}`);
    showTranslation(ui, text, translatedText, subtitleId, timers, state);
    return;
  }

  // Translate
  await translateSubtitle(text, subtitleId, state, ui, timers);
};

// ===================
// KEEP ALL EXISTING HELPER FUNCTIONS
// ===================

const getLanguageName = (langCode) => {
  const languages = {
    en: "English",
    fa: "Persian (Farsi)",
    fr: "French", 
    de: "German",
    es: "Spanish",
    ar: "Arabic",
    zh: "Chinese",
    ru: "Russian",
  };
  return languages[langCode] || langCode;
};

const calculateDisplayTime = (text) => {
  return Math.min(Math.max(text.length * 80, 2000), 6000);
};

const cleanTranslatedText = (text) => text.replace(/^[\"']|[\"']$/g, "").trim();

const isVideoPage = () => window.location.pathname.includes("/watch");

const getToneInstructions = (tone) => {
  const instructions = {
    casual: "Use a casual, friendly, and conversational tone.",
    formal: "Use a formal, professional, and polite tone.", 
    simple: "Use simple, clear language that's easy to understand.",
    literary: "Use a literary, poetic, and expressive tone.",
    neutral: "",
  };
  return instructions[tone] || "";
};

// ===================
// KEEP ALL EXISTING STORAGE FUNCTIONS
// ===================

const loadSettings = () =>
  new Promise((resolve) => {
    if (!browserAPI || !browserAPI.storage) {
      console.warn('⚠️ Browser API not available, using default settings');
      resolve({
        apiKey: "",
        targetLanguage: "fa",
        isEnabled: true,
        translationTone: "neutral",
      });
      return;
    }

    browserAPI.storage.local.get(
      [
        "geminiApiKey",
        "lastTargetLang",
        "youtubeSubtitlesEnabled",
        "videoTranslateTone",
      ],
      (result) => {
        resolve({
          apiKey: result.geminiApiKey || "",
          targetLanguage: result.lastTargetLang || "fa",
          isEnabled: result.youtubeSubtitlesEnabled !== false,
          translationTone: result.videoTranslateTone || "neutral",
        });
      }
    );
  });

const loadCustomizationSettings = () =>
  new Promise((resolve) => {
    if (!browserAPI || !browserAPI.storage) {
      console.warn('⚠️ Browser API not available, using default customization settings');
      resolve({
        subtitleFontSize: "16px",
        subtitleOpacity: "0.8"
      });
      return;
    }

    browserAPI.storage.local.get(
      [
        "subtitleFontSize",
        "subtitleOpacity",
      ],
      (result) => {
        resolve(result || {});
      }
    );
  });

const saveCustomizationSettings = (settings) => {
  if (!browserAPI || !browserAPI.storage) {
    console.warn('⚠️ Browser API not available, cannot save settings');
    return;
  }

  browserAPI.storage.local.set({
    subtitleFontSize: settings.fontSize,
    subtitleOpacity: settings.opacity,
  });
};

// ===================
// KEEP ALL EXISTING UI FUNCTIONS
// ===================

const createTranslationContainer = () => {
  const container = document.createElement("div");
  if (container.style) {
    Object.assign(container.style, {
      position: "fixed",
      bottom: "120px",
      left: "0",
      width: "100%",
      textAlign: "center",
      zIndex: "9999",
      pointerEvents: "none",
      display: "none",
    });
  }
  return container;
};

const createSubtitleElement = (isOriginal = false) => {
  const element = document.createElement("div");
  if (element.style) {
    Object.assign(element.style, {
      backgroundColor: isOriginal
        ? "rgba(0, 0, 0, 0.8)"
        : "rgba(26, 115, 232, 0.8)",
      color: "white",
      padding: "8px 12px",
      borderRadius: "4px",
      display: "inline-block",
      maxWidth: "80%",
      fontSize: "16px",
      lineHeight: "1.4",
      fontFamily: "Tahoma, Arial, sans-serif",
      textShadow: "1px 1px 1px rgba(0, 0, 0, 0.5)",
      wordWrap: "break-word",
      whiteSpace: "pre-wrap",
      margin: "2px 0",
    });
  }

  if (!isOriginal && element.dir !== undefined) {
    element.dir = "rtl";
  }

  return element;
};

const createToggleButton = () => {
  const button = document.createElement("button");
  if (button.style) {
    Object.assign(button.style, {
      position: "fixed",
      bottom: "20px",
      right: "20px",
      zIndex: "10000",
      backgroundColor: "rgba(26, 115, 232, 0.9)",
      color: "white",
      border: "none",
      borderRadius: "20px",
      padding: "10px 15px",
      fontSize: "14px",
      cursor: "pointer",
      pointerEvents: "auto",
      fontFamily: "Arial, sans-serif",
      boxShadow: "0 2px 10px rgba(0,0,0,0.3)",
      transition: "all 0.3s ease",
    });
  }

  if (button.innerHTML !== undefined) button.innerHTML = "🔄 Toggle Original";
  if (button.title !== undefined) button.title = "Toggle original subtitles";

  return button;
};

const createSettingsPanel = () => {
  const panel = document.createElement("div");
  Object.assign(panel.style, {
    position: "fixed",
    top: "20px",
    right: "20px",
    width: "300px",
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    color: "white",
    padding: "20px",
    borderRadius: "10px",
    zIndex: "10001",
    display: "none",
    fontFamily: "Arial, sans-serif",
    fontSize: "14px",
    pointerEvents: "auto",
    boxShadow: "0 4px 20px rgba(0,0,0,0.5)",
  });

  panel.innerHTML = `
    <h3 style="margin-top: 0; color: #1a73e8;">Subtitle Settings</h3>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">Font Size:</label>
      <input type="range" id="fontSize" min="12" max="24" value="16" style="width: 100%;">
      <span id="fontSizeValue">16px</span>
    </div>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">Background Color:</label>
      <input type="color" id="bgColor" value="#1a73e8" style="width: 100%; height: 30px;">
    </div>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">Text Color:</label>
      <input type="color" id="textColor" value="#ffffff" style="width: 100%; height: 30px;">
    </div>
    <div style="margin-bottom: 15px;">
      <label style="display: block; margin-bottom: 5px;">Opacity:</label>
      <input type="range" id="opacity" min="0.1" max="1" step="0.1" value="0.8" style="width: 100%;">
      <span id="opacityValue">0.8</span>
    </div>
    <button id="closeSettings" style="background: #1a73e8; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Close</button>
  `;

  return panel;
};

const createSettingsButton = () => {
  const button = document.createElement("button");
  Object.assign(button.style, {
    position: "fixed",
    bottom: "70px",
    right: "20px",
    zIndex: "10000",
    backgroundColor: "rgba(108, 117, 125, 0.9)",
    color: "white",
    border: "none",
    borderRadius: "20px",
    padding: "10px 15px",
    fontSize: "14px",
    cursor: "pointer",
    pointerEvents: "auto",
    fontFamily: "Arial, sans-serif",
    boxShadow: "0 2px 10px rgba(0,0,0,0.3)",
    transition: "all 0.3s ease",
  });

  button.innerHTML = "⚙️ Settings";
  button.title = "Subtitle settings";

  return button;
};

// ===================
// KEEP ALL UI MANIPULATION FUNCTIONS
// ===================

const applyCustomization = (ui, settings) => {
  if (!ui.subtitleElement) return;

  const element = ui.subtitleElement;
  if (settings.fontSize) element.style.fontSize = settings.fontSize;
  if (settings.bgColor) element.style.backgroundColor = settings.bgColor;
  if (settings.textColor) element.style.color = settings.textColor;
  if (settings.opacity) element.style.opacity = settings.opacity;
};

const showTranslation = (
  ui,
  originalText,
  translatedText,
  subtitleId,
  timers,
  state
) => {
  if (!ui.translationDiv || !ui.subtitleElement) return;

  // Clear existing timer
  if (timers.subtitleTimers.has(subtitleId)) {
    clearTimeout(timers.subtitleTimers.get(subtitleId));
  }

  // Show translated text
  ui.subtitleElement.textContent = translatedText;

  // Show original text if enabled
  if (state.showOriginal && ui.originalElement) {
    ui.originalElement.textContent = originalText;
    ui.originalElement.style.display = "block";
  } else if (ui.originalElement) {
    ui.originalElement.style.display = "none";
  }

  ui.translationDiv.style.display = "block";

  // Set timer to hide
  const displayTime = calculateDisplayTime(originalText);
  const timerId = setTimeout(() => {
    if (subtitleId === state.currentSubtitleId && ui.translationDiv) {
      ui.translationDiv.style.display = "none";
    }
    timers.subtitleTimers.delete(subtitleId);
  }, displayTime);

  timers.subtitleTimers.set(subtitleId, timerId);
};

const setupUI = async (ui, state) => {
  // Remove existing UI
  if (ui.translationDiv) {
    ui.translationDiv.remove();
  }

  // Create main container
  ui.translationDiv = createTranslationContainer();
  document.body.appendChild(ui.translationDiv);

  // Create subtitle elements
  ui.subtitleElement = createSubtitleElement(false);
  ui.originalElement = createSubtitleElement(true);

  // Add elements to container
  ui.translationDiv.appendChild(ui.originalElement);
  ui.translationDiv.appendChild(ui.subtitleElement);

  // Create toggle button
  ui.toggleButton = createToggleButton();
  document.body.appendChild(ui.toggleButton);

  // Create settings panel
  ui.settingsPanel = createSettingsPanel();
  document.body.appendChild(ui.settingsPanel);

  // Create settings button
  const settingsButton = createSettingsButton();
  document.body.appendChild(settingsButton);

  // Setup event listeners
  ui.toggleButton.addEventListener("click", () => {
    state.showOriginal = !state.showOriginal;
    ui.toggleButton.innerHTML = state.showOriginal
      ? "🔄 Hide Original"
      : "🔄 Show Original";
    ui.toggleButton.style.backgroundColor = state.showOriginal
      ? "rgba(220, 53, 69, 0.9)"
      : "rgba(26, 115, 232, 0.9)";
  });

  settingsButton.addEventListener("click", () => {
    ui.settingsPanel.style.display =
      ui.settingsPanel.style.display === "none" ? "block" : "none";
  });

  // Setup settings panel event listeners
  setupSettingsPanel(ui, state);

  // Load and apply customization
  const customSettings = await loadCustomizationSettings();
  applyCustomization(ui, customSettings);
};

const setupSettingsPanel = (ui, state) => {
  const panel = ui.settingsPanel;
  if (!panel || !panel.querySelector) return;

  // Font size slider
  const fontSizeSlider = panel.querySelector("#fontSize");
  const fontSizeValue = panel.querySelector("#fontSizeValue");

  if (fontSizeSlider && fontSizeSlider.addEventListener) {
    fontSizeSlider.addEventListener("input", (e) => {
      const value = e.target.value + "px";
      if (fontSizeValue) fontSizeValue.textContent = value;
      if (ui.subtitleElement && ui.subtitleElement.style) ui.subtitleElement.style.fontSize = value;
      if (ui.originalElement && ui.originalElement.style) ui.originalElement.style.fontSize = value;
    });
  }

  // Background color
  const bgColorPicker = panel.querySelector("#bgColor");
  if (bgColorPicker && bgColorPicker.addEventListener) {
    bgColorPicker.addEventListener("change", (e) => {
      const color = e.target.value;
      if (ui.subtitleElement && ui.subtitleElement.style) {
        ui.subtitleElement.style.backgroundColor = color + "cc"; // Add alpha
      }
    });
  }

  // Text color
  const textColorPicker = panel.querySelector("#textColor");
  if (textColorPicker && textColorPicker.addEventListener) {
    textColorPicker.addEventListener("change", (e) => {
      const color = e.target.value;
      if (ui.subtitleElement && ui.subtitleElement.style) ui.subtitleElement.style.color = color;
      if (ui.originalElement && ui.originalElement.style) ui.originalElement.style.color = color;
    });
  }

  // Opacity slider
  const opacitySlider = panel.querySelector("#opacity");
  const opacityValue = panel.querySelector("#opacityValue");

  if (opacitySlider && opacitySlider.addEventListener) {
    opacitySlider.addEventListener("input", (e) => {
      const value = e.target.value;
      if (opacityValue) opacityValue.textContent = value;
      if (ui.subtitleElement && ui.subtitleElement.style) ui.subtitleElement.style.opacity = value;
      if (ui.originalElement && ui.originalElement.style) ui.originalElement.style.opacity = value;
    });
  }

  // Close button
  const closeButton = panel.querySelector("#closeSettings");
  if (closeButton && closeButton.addEventListener) {
    closeButton.addEventListener("click", () => {
      if (panel.style) panel.style.display = "none";

      // Save settings
      saveCustomizationSettings({
        fontSize: (fontSizeSlider && fontSizeSlider.value) ? fontSizeSlider.value + "px" : "16px",
        bgColor: (bgColorPicker && bgColorPicker.value) ? bgColorPicker.value + "cc" : "#1a73e8cc",
        textColor: (textColorPicker && textColorPicker.value) ? textColorPicker.value : "#ffffff",
        opacity: (opacitySlider && opacitySlider.value) ? opacitySlider.value : "0.8",
      });
    });
  }
};

// ===================
// KEEP ORIGINAL TRANSLATION FUNCTION
// ===================

const translateSubtitle = async (text, subtitleId, state, ui, timers) => {
  if (!state.apiKey || state.isTranslating) return;

  state.isTranslating = true;

  try {
    let prompt = `Translate this YouTube subtitle to ${getLanguageName(
      state.targetLanguage
    )}`;

    const toneInstruction = getToneInstructions(state.translationTone);
    if (toneInstruction) {
      prompt += `. ${toneInstruction}`;
    }

    prompt += `. Return ONLY the translated text: "${text}"`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${state.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Priority: "high",
        },
        signal: controller.signal,
        body: JSON.stringify({
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 1024,
          },
        }),
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
      const translatedText = cleanTranslatedText(
        data.candidates[0].content.parts[0].text
      );

      // Cache translation
      state.subtitleCache.set(text, translatedText);

      // Also log to console with timing
      const video = document.querySelector('video');
      const currentTime = video ? video.currentTime : 0;
      console.log(`🔄 [${formatTime(currentTime)}] Translation: ${translatedText}`);

      // Show translation if still current
      if (subtitleId >= state.currentSubtitleId - 2) {
        showTranslation(ui, text, translatedText, subtitleId, timers, state);
      }
    }
  } catch (error) {
    if (error.name !== "AbortError") {
      console.error("Translation error:", error);
    }
  } finally {
    state.isTranslating = false;
  }
};

// ===================
// KEEP EXISTING PROCESSING FUNCTIONS
// ===================

const processNewSubtitle = async (text, state, ui, timers) => {
  state.lastSubtitleText = text;
  state.currentSubtitleId++;
  const subtitleId = state.currentSubtitleId;

  // Check cache
  if (state.subtitleCache.has(text)) {
    showTranslation(
      ui,
      text,
      state.subtitleCache.get(text),
      subtitleId,
      timers,
      state
    );
    return;
  }

  // Translate
  await translateSubtitle(text, subtitleId, state, ui, timers);
};

const observeSubtitleElement = (element, state, ui, timers, observers) => {
  if (observers.mutationObserver) {
    observers.mutationObserver.disconnect();
  }

  observers.mutationObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === "characterData" || mutation.type === "childList") {
        const subtitleText = element.textContent.trim();
        if (subtitleText === state.lastSubtitleText) return;
        processNewSubtitleWithTiming(subtitleText, state, ui, timers);
      }
    });
  });

  observers.mutationObserver.observe(element, {
    characterData: true,
    childList: true,
    subtree: true,
  });
};

// ===================
// KEEP CLEANUP FUNCTIONS
// ===================

const cleanup = (timers, observers) => {
  // Clear intervals
  if (timers.monitoringInterval) {
    clearInterval(timers.monitoringInterval);
    timers.monitoringInterval = null;
  }

  if (timers.urlCheckInterval) {
    clearInterval(timers.urlCheckInterval);
    timers.urlCheckInterval = null;
  }

  // Clear timeouts
  timers.subtitleTimers.forEach((timerId) => clearTimeout(timerId));
  timers.subtitleTimers.clear();

  // Disconnect observers
  if (observers.mutationObserver) {
    observers.mutationObserver.disconnect();
    observers.mutationObserver = null;
  }
};

const resetSubtitleState = (state, timers) => {
  state.lastSubtitleText = "";
  state.currentSubtitleElement = null;
  state.currentSubtitleId = 0;
  state.isTranslating = false;
  state.videoDetected = false;
  state.isVideoPlaying = false;
  state.allSubtitles = [];
  state.subtitleTrack = null;

  timers.subtitleTimers.forEach((timerId) => clearTimeout(timerId));
  timers.subtitleTimers.clear();
};

// ===================
// KEEP EXISTING UI CREATION FUNCTIONS
// ===================

const createInitialUI = () => ({
  translationDiv: null,
  subtitleElement: null,
  originalElement: null,
  toggleButton: null,
  settingsPanel: null,
});

const createInitialObservers = () => ({
  mutationObserver: null,
  urlObserver: null,
});

const createInitialTimers = () => ({
  subtitleTimers: new Map(),
  monitoringInterval: null,
  urlCheckInterval: null,
});

// ===================
// ENHANCED MAIN INITIALIZATION
// ===================

const initializeYouTubeTranslator = async () => {
  console.log('🚀 Initializing YouTube Translator...');

  // Initialize state
  const state = createInitialState();
  const ui = createInitialUI();
  const observers = createInitialObservers();
  const timers = createInitialTimers();

  // Load settings
  const settings = await loadSettings();
  Object.assign(state, settings);

  console.log('⚙️ Settings loaded:', settings);

  // Check if on YouTube
  if (!window.location.hostname.includes("youtube.com")) {
    console.log('❌ Not on YouTube, exiting...');
    return;
  }

  // Check if enabled
  if (!state.isEnabled) {
    console.log('❌ YouTube subtitles disabled, exiting...');
    return;
  }

  // Check if API key exists
  if (!state.apiKey) {
    console.log('⚠️ No API key found, translation will not work');
    console.log('💡 Please set your Gemini API key in the extension settings');
  }

  console.log('✅ All checks passed, setting up UI and monitoring...');

  try {
    // Setup UI
    await setupUI(ui, state);
    console.log('✅ UI setup complete');

    // Start monitoring
    startEnhancedSubtitleMonitoring(state, ui, timers, observers);
    console.log('✅ Subtitle monitoring started');

    // Handle URL changes (for SPA navigation)
    let currentUrl = window.location.href;
    timers.urlCheckInterval = setInterval(() => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        console.log('🔄 URL changed, resetting state...');
        resetSubtitleState(state, timers);

        // Restart monitoring if on video page
        if (isVideoPage()) {
          startEnhancedSubtitleMonitoring(state, ui, timers, observers);
        }
      }
    }, 1000);

    console.log('🎉 YouTube Translator initialized successfully!');

  } catch (error) {
    console.error('❌ Error initializing YouTube Translator:', error);
  }
};

// ===================
// AUTO-INITIALIZATION
// ===================

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeYouTubeTranslator);
} else {
  // DOM is already ready
  initializeYouTubeTranslator();
}

// Also initialize on page navigation (for YouTube's SPA)
window.addEventListener('yt-navigate-finish', initializeYouTubeTranslator);

// Fallback for manual initialization
window.initializeYouTubeTranslator = initializeYouTubeTranslator;
