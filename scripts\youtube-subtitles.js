// YouTube Subtitles Translator - Timeline Based Version
console.log('🎬 YouTube Timeline Subtitles Script Starting...');

// Check if we're on YouTube
if (!window.location.hostname.includes('youtube.com')) {
    console.log('❌ Not on YouTube, exiting');
} else {
    console.log('✅ On YouTube, initializing timeline-based translator...');
    initializeTimelineTranslator();
}

function initializeTimelineTranslator() {
    const browserAPI = typeof browser !== "undefined" ? browser : (typeof chrome !== "undefined" ? chrome : null);
    
    // Timeline-based state management
    let apiKey = '';
    let targetLanguage = 'fa';
    let translationDiv = null;
    let allSubtitles = []; // Array of {startTime, endTime, text, translatedText}
    let isTranslating = false;
    let currentDisplayedIndex = -1;
    let timeUpdateInterval = null;
    let videoElement = null;
    
    // Initialize
    loadSettings();
    createTranslationOverlay();
    startVideoMonitoring();
    
    function loadSettings() {
        if (browserAPI && browserAPI.storage) {
            browserAPI.storage.local.get(['geminiApiKey', 'lastTargetLang'], (result) => {
                apiKey = result.geminiApiKey || '';
                targetLanguage = result.lastTargetLang || 'fa';
                console.log('⚙️ Settings loaded - API Key:', apiKey ? 'Available' : 'Missing');
                
                // Start subtitle extraction after settings are loaded
                if (apiKey) {
                    setTimeout(extractAllSubtitles, 2000);
                }
            });
        } else {
            console.log('⚠️ Browser API not available');
        }
    }
    
    function createTranslationOverlay() {
        // Remove existing overlay
        const existing = document.getElementById('youtube-translation-overlay');
        if (existing) existing.remove();
        
        // Create new overlay
        translationDiv = document.createElement('div');
        translationDiv.id = 'youtube-translation-overlay';
        translationDiv.style.cssText = `
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 20px;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            font-weight: 500;
            z-index: 10000;
            display: none;
            max-width: 85%;
            min-width: 200px;
            text-align: center;
            direction: rtl;
            line-height: 1.5;
            border: 2px solid #1a73e8;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
            word-wrap: break-word;
            white-space: pre-wrap;
        `;
        
        document.body.appendChild(translationDiv);
        console.log('✅ Translation overlay created');
    }
    
    function startVideoMonitoring() {
        console.log('🎥 Starting video monitoring...');
        
        const checkVideo = () => {
            videoElement = document.querySelector('video');
            if (videoElement && !timeUpdateInterval) {
                console.log('✅ Video element found, starting timeline sync');
                startTimelineSync();
            }
        };
        
        // Check immediately and then periodically
        checkVideo();
        setInterval(checkVideo, 2000);
    }
    
    function startTimelineSync() {
        if (timeUpdateInterval) {
            clearInterval(timeUpdateInterval);
        }
        
        timeUpdateInterval = setInterval(() => {
            if (videoElement && allSubtitles.length > 0) {
                const currentTime = videoElement.currentTime;
                displaySubtitleForTime(currentTime);
            }
        }, 100); // Check every 100ms for smooth display
        
        console.log('⏰ Timeline sync started');
    }
    
    async function extractAllSubtitles() {
        console.log('📝 Starting subtitle extraction...');
        
        if (!videoElement) {
            videoElement = document.querySelector('video');
        }
        
        if (!videoElement) {
            console.log('❌ No video element found');
            return;
        }
        
        // Try to get subtitle tracks from video element
        const tracks = videoElement.textTracks;
        let subtitleTrack = null;
        
        for (let i = 0; i < tracks.length; i++) {
            const track = tracks[i];
            if (track.kind === 'subtitles' || track.kind === 'captions') {
                track.mode = 'showing';
                subtitleTrack = track;
                break;
            }
        }
        
        if (subtitleTrack && subtitleTrack.cues) {
            console.log(`📋 Found ${subtitleTrack.cues.length} subtitle cues`);
            
            // Extract all subtitles with timing
            allSubtitles = [];
            for (let i = 0; i < subtitleTrack.cues.length; i++) {
                const cue = subtitleTrack.cues[i];
                allSubtitles.push({
                    startTime: cue.startTime,
                    endTime: cue.endTime,
                    text: cue.text,
                    translatedText: null,
                    index: i
                });
            }
            
            console.log(`✅ Extracted ${allSubtitles.length} subtitles`);
            
            // Start translation process
            if (apiKey) {
                await translateAllSubtitles();
            }
        } else {
            console.log('⚠️ No subtitle track found, trying DOM extraction...');
            // Fallback to DOM-based extraction
            setTimeout(extractAllSubtitles, 5000);
        }
    }
    
    async function translateAllSubtitles() {
        if (isTranslating || allSubtitles.length === 0) return;
        
        isTranslating = true;
        console.log(`🌐 Starting translation of ${allSubtitles.length} subtitles...`);
        
        // Show progress
        showTranslation('🔄 در حال ترجمه زیرنویس‌ها...', true);
        
        let translatedCount = 0;
        const batchSize = 5; // Translate in batches to avoid rate limiting
        
        for (let i = 0; i < allSubtitles.length; i += batchSize) {
            const batch = allSubtitles.slice(i, i + batchSize);
            const promises = batch.map(subtitle => translateSingleSubtitle(subtitle));
            
            await Promise.all(promises);
            translatedCount += batch.length;
            
            // Update progress
            const progress = Math.round((translatedCount / allSubtitles.length) * 100);
            showTranslation(`🔄 ترجمه شده: ${translatedCount}/${allSubtitles.length} (${progress}%)`, true);
            
            // Small delay between batches
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        isTranslating = false;
        console.log('✅ All subtitles translated!');
        showTranslation('✅ ترجمه کامل شد!', false);
        
        // Hide success message after 2 seconds
        setTimeout(() => {
            if (translationDiv) {
                translationDiv.style.display = 'none';
            }
        }, 2000);
    }
    
    async function translateSingleSubtitle(subtitle) {
        if (!subtitle.text || subtitle.translatedText) return;
        
        try {
            const prompt = `Translate this English subtitle to Persian (Farsi). Requirements:

1. Translate the COMPLETE text, do not cut it short
2. Use natural, fluent Persian
3. Maintain the original meaning and tone
4. Return ONLY the Persian translation, nothing else

English text: "${subtitle.text}"

Persian translation:`;
            
            const response = await fetch(
                `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 4096,
                            topP: 0.9,
                            topK: 50
                        },
                    }),
                }
            );
            
            if (response.ok) {
                const data = await response.json();
                if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
                    let translation = data.candidates[0].content.parts[0].text.trim();
                    
                    // Clean up translation
                    translation = translation.replace(/^["']|["']$/g, '');
                    translation = translation.replace(/^\s*Persian translation:\s*/i, '');
                    translation = translation.replace(/^\s*Translation:\s*/i, '');
                    translation = translation.trim();
                    
                    subtitle.translatedText = translation;
                    console.log(`✅ Translated [${subtitle.index}]: ${subtitle.text} → ${translation}`);
                }
            }
        } catch (error) {
            console.error(`❌ Translation error for subtitle ${subtitle.index}:`, error);
        }
    }
    
    function displaySubtitleForTime(currentTime) {
        // Find the subtitle that should be displayed at current time
        let targetIndex = -1;
        
        for (let i = 0; i < allSubtitles.length; i++) {
            const subtitle = allSubtitles[i];
            if (currentTime >= subtitle.startTime && currentTime <= subtitle.endTime) {
                targetIndex = i;
                break;
            }
        }
        
        // Only update if the subtitle changed
        if (targetIndex !== currentDisplayedIndex) {
            currentDisplayedIndex = targetIndex;
            
            if (targetIndex >= 0) {
                const subtitle = allSubtitles[targetIndex];
                const translation = subtitle.translatedText || subtitle.text;
                showTranslation(translation, false);
                
                console.log(`📺 [${formatTime(currentTime)}] Showing: ${translation}`);
            } else {
                // Hide subtitle when not in any time range
                if (translationDiv) {
                    translationDiv.style.display = 'none';
                }
            }
        }
    }
    
    function showTranslation(text, isLoading = false) {
        if (translationDiv) {
            translationDiv.textContent = text;
            translationDiv.style.display = 'block';
            
            // Different styling for loading vs translated
            if (isLoading) {
                translationDiv.style.background = 'rgba(255, 152, 0, 0.9)';
                translationDiv.style.borderColor = '#ff9800';
            } else {
                translationDiv.style.background = 'rgba(0, 0, 0, 0.9)';
                translationDiv.style.borderColor = '#1a73e8';
            }
        }
    }
    
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('📄 DOM loaded, reinitializing...');
        if (window.location.hostname.includes('youtube.com')) {
            initializeTimelineTranslator();
        }
    });
}

// Handle YouTube navigation
window.addEventListener('yt-navigate-finish', () => {
    console.log('🔄 YouTube navigation detected, reinitializing...');
    setTimeout(() => {
        if (window.location.hostname.includes('youtube.com')) {
            initializeTimelineTranslator();
        }
    }, 1000);
});

console.log('🎉 YouTube Timeline Subtitles Script Loaded Successfully');
