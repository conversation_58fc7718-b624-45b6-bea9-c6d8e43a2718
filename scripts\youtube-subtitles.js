// YouTube Subtitles Translator - Simple Version
console.log('🎬 YouTube Subtitles Script Starting...');

// Check if we're on YouTube
if (!window.location.hostname.includes('youtube.com')) {
    console.log('❌ Not on YouTube, exiting');
} else {
    console.log('✅ On YouTube, initializing...');
    initializeSimpleTranslator();
}

function initializeSimpleTranslator() {
    const browserAPI = typeof browser !== "undefined" ? browser : (typeof chrome !== "undefined" ? chrome : null);
    
    // Simple state management
    let currentSubtitle = '';
    let apiKey = '';
    let targetLanguage = 'fa';
    let translationCache = new Map();
    let isTranslating = false;
    let translationDiv = null;
    
    // Load settings
    loadSimpleSettings();
    
    // Create translation overlay
    createTranslationOverlay();
    
    // Start monitoring
    startSubtitleMonitoring();
    
    function loadSimpleSettings() {
        if (browserAPI && browserAPI.storage) {
            browserAPI.storage.local.get(['geminiApiKey', 'lastTargetLang'], (result) => {
                apiKey = result.geminiApiKey || '';
                targetLanguage = result.lastTargetLang || 'fa';
                console.log('⚙️ Settings loaded - API Key:', apiKey ? 'Available' : 'Missing');
            });
        } else {
            console.log('⚠️ Browser API not available');
        }
    }
    
    function createTranslationOverlay() {
        // Remove existing overlay
        const existing = document.getElementById('youtube-translation-overlay');
        if (existing) existing.remove();
        
        // Create new overlay
        translationDiv = document.createElement('div');
        translationDiv.id = 'youtube-translation-overlay';
        translationDiv.style.cssText = `
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 20px;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            font-weight: 500;
            z-index: 10000;
            display: none;
            max-width: 85%;
            min-width: 200px;
            text-align: center;
            direction: rtl;
            line-height: 1.5;
            border: 2px solid #1a73e8;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
            word-wrap: break-word;
            white-space: pre-wrap;
        `;
        
        document.body.appendChild(translationDiv);
        console.log('✅ Translation overlay created');
    }
    
    function startSubtitleMonitoring() {
        console.log('🔍 Starting subtitle monitoring...');

        let monitorCount = 0;
        let lastProcessedTime = 0;
        let currentObserver = null;

        // Function to setup mutation observer for a subtitle element
        function setupObserver(element) {
            if (currentObserver) {
                currentObserver.disconnect();
            }

            currentObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' || mutation.type === 'characterData') {
                        const subtitleText = element.textContent.trim();
                        const currentTime = Date.now();

                        if (subtitleText &&
                            subtitleText !== currentSubtitle &&
                            (currentTime - lastProcessedTime) > 800) {

                            console.log('👁️ Observer detected new subtitle:', subtitleText);
                            currentSubtitle = subtitleText;
                            lastProcessedTime = currentTime;
                            handleNewSubtitle(subtitleText);
                        }
                    }
                });
            });

            currentObserver.observe(element, {
                childList: true,
                characterData: true,
                subtree: true
            });
        }

        // Polling fallback
        setInterval(() => {
            monitorCount++;

            // Find subtitle element with multiple selectors
            const selectors = [
                '.ytp-caption-segment',
                '.caption-window',
                '.ytp-caption-window-container .ytp-caption-segment',
                '.html5-video-player .ytp-caption-segment',
                '[class*="caption-segment"]'
            ];

            let subtitleElement = null;
            for (const selector of selectors) {
                subtitleElement = document.querySelector(selector);
                if (subtitleElement) break;
            }

            // Setup observer for new elements
            if (subtitleElement && !currentObserver) {
                console.log('🎯 Setting up observer for subtitle element');
                setupObserver(subtitleElement);
            }

            // Log every 60 iterations (30 seconds)
            if (monitorCount % 60 === 0) {
                console.log('🔄 Monitoring... Subtitle found:', !!subtitleElement, 'Observer active:', !!currentObserver);
            }

            // Fallback polling check
            if (subtitleElement) {
                const subtitleText = subtitleElement.textContent.trim();
                const currentTime = Date.now();

                if (subtitleText &&
                    subtitleText !== currentSubtitle &&
                    (currentTime - lastProcessedTime) > 1200) {

                    console.log('📝 Polling detected new subtitle:', subtitleText);
                    currentSubtitle = subtitleText;
                    lastProcessedTime = currentTime;
                    handleNewSubtitle(subtitleText);
                }
            }
        }, 500);
    }
    
    async function handleNewSubtitle(text) {
        // Check cache first for instant display
        if (translationCache.has(text)) {
            const translation = translationCache.get(text);
            console.log('💾 Using cached translation:', translation);
            showTranslation(translation);
            return;
        }

        // Show loading indicator
        showTranslation('🔄 در حال ترجمه...', true);

        // Translate if API key is available
        if (apiKey && !isTranslating) {
            await translateText(text);
        } else if (!apiKey) {
            showTranslation('⚠️ کلید API تنظیم نشده - به تنظیمات افزونه بروید');
        }
    }
    
    async function translateText(text) {
        if (isTranslating) return;

        isTranslating = true;
        console.log('🌐 Translating:', text);

        try {
            // Enhanced prompt for better translation quality
            const prompt = `You are a professional translator. Translate the following English text to Persian (Farsi) with high accuracy and natural flow.

Rules:
- Provide ONLY the Persian translation
- Keep the meaning and tone intact
- Use natural Persian expressions
- Do not add explanations or notes

Text to translate: "${text}"`;

            const response = await fetch(
                `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{ parts: [{ text: prompt }] }],
                        generationConfig: {
                            temperature: 0.2, // Slightly higher for more natural language
                            maxOutputTokens: 2048, // More tokens for longer subtitles
                            topP: 0.8,
                            topK: 40
                        },
                    }),
                }
            );

            if (response.ok) {
                const data = await response.json();
                if (data.candidates && data.candidates[0]?.content?.parts[0]?.text) {
                    let translation = data.candidates[0].content.parts[0].text.trim();

                    // Clean up the translation
                    translation = translation.replace(/^["']|["']$/g, ''); // Remove quotes
                    translation = translation.replace(/^\s*ترجمه:\s*/i, ''); // Remove "Translation:" prefix
                    translation = translation.replace(/^\s*متن ترجمه شده:\s*/i, ''); // Remove Persian prefix

                    // Cache the translation
                    translationCache.set(text, translation);

                    // Show translation
                    showTranslation(translation);

                    console.log('✅ Translation:', translation);
                } else {
                    console.error('❌ Invalid API response:', data);
                    showTranslation('❌ ترجمه ناموفق - پاسخ نامعتبر');
                }
            } else {
                console.error('❌ Translation failed:', response.status);
                const errorText = await response.text();
                console.error('Error details:', errorText);
                showTranslation('❌ ترجمه ناموفق - خطای API');
            }
        } catch (error) {
            console.error('❌ Translation error:', error);
            showTranslation('❌ ترجمه ناموفق - خطای شبکه');
        } finally {
            isTranslating = false;
        }
    }
    
    let hideTimeout = null;

    function showTranslation(translation, isLoading = false) {
        if (translationDiv) {
            translationDiv.textContent = translation;
            translationDiv.style.display = 'block';

            // Clear previous timeout
            if (hideTimeout) {
                clearTimeout(hideTimeout);
            }

            // Different styling for loading vs translated
            if (isLoading) {
                translationDiv.style.background = 'rgba(255, 152, 0, 0.9)';
                translationDiv.style.borderColor = '#ff9800';
                translationDiv.style.color = 'white';
            } else {
                translationDiv.style.background = 'rgba(0, 0, 0, 0.9)';
                translationDiv.style.borderColor = '#1a73e8';
                translationDiv.style.color = 'white';
            }

            // Calculate display time based on text length (minimum 4 seconds, maximum 10 seconds)
            const displayTime = Math.max(4000, Math.min(translation.length * 100, 10000));

            // Hide after calculated time
            hideTimeout = setTimeout(() => {
                if (translationDiv) {
                    translationDiv.style.display = 'none';
                }
            }, displayTime);

            console.log(`📺 Showing translation for ${displayTime}ms:`, translation);
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('📄 DOM loaded, reinitializing...');
        if (window.location.hostname.includes('youtube.com')) {
            initializeSimpleTranslator();
        }
    });
}

// Handle YouTube navigation
window.addEventListener('yt-navigate-finish', () => {
    console.log('🔄 YouTube navigation detected, reinitializing...');
    setTimeout(() => {
        if (window.location.hostname.includes('youtube.com')) {
            initializeSimpleTranslator();
        }
    }, 1000);
});

console.log('🎉 YouTube Subtitles Script Loaded Successfully');
