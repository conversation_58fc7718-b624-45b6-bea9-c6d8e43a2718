<!DOCTYPE html>
<html>
<head>
    <title>Basic Test</title>
</head>
<body>
    <h1>Basic Test - Check Console</h1>
    
    <script>
        console.log('=== STARTING BASIC TEST ===');
        
        // Create minimal mocks
        if (!window.chrome) {
            window.chrome = {
                storage: {
                    local: {
                        get: function(keys, callback) {
                            console.log('Mock chrome.storage.local.get called');
                            setTimeout(() => {
                                callback({
                                    geminiApiKey: "test-api-key",
                                    lastTargetLang: "fa",
                                    youtubeSubtitlesEnabled: true,
                                    videoTranslateTone: "neutral"
                                });
                            }, 10);
                        },
                        set: function(data, callback) {
                            console.log('Mock chrome.storage.local.set called with:', data);
                            if (callback) callback();
                        }
                    }
                }
            };
        }
        
        console.log('Chrome mock created');
    </script>
    
    <script>
        // Test if we can access the script
        console.log('About to load youtube-subtitles.js');
    </script>
    
    <script src="scripts/youtube-subtitles.js"></script>
    
    <script>
        console.log('youtube-subtitles.js loaded');
        console.log('Available functions:', Object.keys(window).filter(key => key.includes('YouTube') || key.includes('initialize')));
        
        // Try to call the function directly
        setTimeout(() => {
            console.log('Attempting manual call...');
            try {
                if (typeof initializeYouTubeTranslator === 'function') {
                    console.log('Function found, calling...');
                    initializeYouTubeTranslator();
                } else {
                    console.log('Function not found in global scope');
                }
            } catch (error) {
                console.error('Error calling function:', error);
            }
        }, 1000);
    </script>
</body>
</html>
