# راهنمای ترجمه زیرنویس مبتنی بر Timeline

## 🚀 ساختار جدید

### مراحل کارکرد:
1. **استخراج کامل زیرنویس‌ها** با timeline از ویدیو
2. **ترجمه یکجای همه زیرنویس‌ها** در پس‌زمینه
3. **نمایش همزمان** با پخش ویدیو بر اساس زمان

## 🔧 ویژگی‌های کلیدی

### ✅ **استخراج Timeline:**
- دریافت کامل `textTracks` از عنصر video
- استخراج `startTime`, `endTime`, `text` برای هر زیرنویس
- ذخیره در آرایه `allSubtitles`

### ✅ **ترجمه Batch:**
- ترجمه همه زیرنویس‌ها در گروه‌های 5 تایی
- جلوگیری از rate limiting
- نمایش پیشرفت ترجمه

### ✅ **نمایش همزمان:**
- بررسی زمان ویدیو هر 100ms
- نمایش زیرنویس مناسب بر اساس `currentTime`
- مخفی کردن خودکار در خارج از بازه زمانی

## 📊 ساختار داده

```javascript
allSubtitles = [
    {
        startTime: 0.0,
        endTime: 3.0,
        text: "Hello, welcome to our channel",
        translatedText: "سلام، به کانال ما خوش آمدید",
        index: 0
    },
    {
        startTime: 3.0,
        endTime: 6.0,
        text: "Today we will learn something new",
        translatedText: "امروز چیز جدیدی یاد خواهیم گرفت",
        index: 1
    }
    // ...
]
```

## 🎯 مزایای ساختار جدید

### 🚀 **سرعت:**
- ترجمه یکبار در ابتدا
- نمایش فوری بدون تاخیر
- عدم نیاز به ترجمه مکرر

### 🎪 **دقت:**
- همزمانی کامل با ویدیو
- نمایش دقیق بر اساس timeline
- عدم از دست رفتن زیرنویس‌ها

### 💾 **بهینگی:**
- کش کامل ترجمه‌ها
- کاهش درخواست‌های API
- مصرف کمتر منابع

## 🔍 نحوه کارکرد

### 1. **مرحله استخراج:**
```javascript
// پیدا کردن subtitle track
const tracks = videoElement.textTracks;
const subtitleTrack = tracks[0]; // اولین track

// استخراج همه cue ها
for (let i = 0; i < subtitleTrack.cues.length; i++) {
    const cue = subtitleTrack.cues[i];
    allSubtitles.push({
        startTime: cue.startTime,
        endTime: cue.endTime,
        text: cue.text,
        translatedText: null,
        index: i
    });
}
```

### 2. **مرحله ترجمه:**
```javascript
// ترجمه در batch های 5 تایی
for (let i = 0; i < allSubtitles.length; i += 5) {
    const batch = allSubtitles.slice(i, i + 5);
    await Promise.all(batch.map(translateSingleSubtitle));
    
    // نمایش پیشرفت
    const progress = Math.round((i / allSubtitles.length) * 100);
    showProgress(progress);
}
```

### 3. **مرحله نمایش:**
```javascript
// بررسی هر 100ms
setInterval(() => {
    const currentTime = videoElement.currentTime;
    
    // پیدا کردن زیرنویس مناسب
    const subtitle = allSubtitles.find(s => 
        currentTime >= s.startTime && currentTime <= s.endTime
    );
    
    if (subtitle) {
        showTranslation(subtitle.translatedText);
    }
}, 100);
```

## 🎨 رابط کاربری

### **پیام‌های وضعیت:**
- `🔄 در حال ترجمه زیرنویس‌ها...`
- `🔄 ترجمه شده: 15/50 (30%)`
- `✅ ترجمه کامل شد!`

### **نمایش زیرنویس:**
- موقعیت: پایین وسط صفحه
- استایل: پس‌زمینه تیره، متن سفید
- جهت: RTL برای فارسی
- زمان: تا پایان بازه زمانی زیرنویس

## 🔧 تنظیمات

### **Batch Size:** تعداد زیرنویس‌ها در هر گروه ترجمه
```javascript
const batchSize = 5; // قابل تنظیم
```

### **Update Interval:** فاصله بررسی زمان ویدیو
```javascript
setInterval(checkTime, 100); // هر 100ms
```

### **Translation Settings:**
```javascript
generationConfig: {
    temperature: 0.1,      // دقت بالا
    maxOutputTokens: 4096, // ترجمه کامل
    topP: 0.9,
    topK: 50
}
```

## 🐛 عیب‌یابی

### **اگر زیرنویس استخراج نمی‌شود:**
1. بررسی وجود `textTracks` در video element
2. فعال بودن زیرنویس در YouTube (CC button)
3. لاگ `📋 Found X subtitle cues`

### **اگر ترجمه نمی‌شود:**
1. بررسی API key در تنظیمات
2. لاگ `🌐 Starting translation of X subtitles...`
3. بررسی خطاهای شبکه در console

### **اگر نمایش همزمان نیست:**
1. بررسی `⏰ Timeline sync started`
2. لاگ زمان ویدیو و زیرنویس فعلی
3. بررسی `currentTime` و `startTime/endTime`

## 📈 بهبودهای آینده

- پشتیبانی از فرمت‌های مختلف زیرنویس
- تنظیمات سفارشی timeline
- ذخیره ترجمه‌ها در storage محلی
- پشتیبانی از چندین زبان همزمان
